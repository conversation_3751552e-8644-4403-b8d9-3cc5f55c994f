import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Tailwind + clsx helper (same as before)
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/* ------------------------------------------------------------------ */
/*                             JWT HELPERS                            */
/* ------------------------------------------------------------------ */

/** In‑memory cache so we only parse once per tab */
let cachedToken: string | undefined;

/**
 * Extract a JWT that Cognito appended to the url as
 * `?access_token=...`.  
 * Stores the result in `localStorage` so we can still
 * get it after a normal page refresh or route change.
 */
export function getTokenFromUrl(): string | undefined {
  if (typeof window === "undefined") return undefined;

  const params = new URLSearchParams(window.location.search);
  const t = params.get("access_token") || undefined;
  if (t) {
    console.log("🔑 Token extracted from URL", `${t.slice(0, 40)}…`);
    localStorage.setItem("jwt", t);
    // Clean the url (optional, comment out if you need to keep it)
    params.delete("access_token");
    const clean = `${window.location.pathname}${params.toString() ? "?" + params.toString() : ""}${window.location.hash}`;
    window.history.replaceState({}, "", clean);
  }
  return t;
}

/**
 * Base64‑URL → string helper (works in browsers)
 */
function b64urlDecode(segment: string) {
  const pad = segment.length % 4 ? 4 - (segment.length % 4) : 0;
  const base64 = segment.replace(/-/g, "+").replace(/_/g, "/") + "=".repeat(pad);
  return atob(base64);
}

/**
 * Check `exp` claim.
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(b64urlDecode(token.split(".")[1]));
    return payload.exp < Math.floor(Date.now() / 1000);
  } catch (e) {
    console.error("JWT parse error", e);
    return true; // treat as expired / invalid
  }
}

/**
 * Global accessor used by ApiService.  
 * Order of precedence:
 * 1) cached value (set once per session)
 * 2) token freshly parsed from URL
 * 3) value persisted in localStorage by a previous visit
 * 4) hard‑coded fallback in API_CONFIG
 */
export function getToken(): string | undefined {
  if (cachedToken) {
    console.log('🔑 getToken: Using cached token');
    return cachedToken;
  }

  const urlToken = getTokenFromUrl();
  const urlToken = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
//  const urlToken = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  const localStorageToken = typeof window !== "undefined" ? localStorage.getItem("jwt") ?? undefined : undefined;
  const fallbackToken = API_CONFIG.JWT_TOKEN;

  console.log('🔑 getToken: URL token:', urlToken ? 'Found' : 'Not found');
  console.log('🔑 getToken: LocalStorage token:', localStorageToken ? 'Found' : 'Not found');
  console.log('🔑 getToken: Fallback token:', fallbackToken ? 'Available' : 'Not available');

  // Try each source in order: URL -> localStorage -> fallback
  if (urlToken) {
    console.log('🔑 getToken: Using URL token');
    cachedToken = urlToken;
  } else if (localStorageToken) {
    console.log('🔑 getToken: Using localStorage token');
    cachedToken = localStorageToken;
  } else if (fallbackToken) {
    console.log('🔑 getToken: Using fallback token from API_CONFIG');
    cachedToken = fallbackToken;
  } else {
    console.error('🚫 getToken: No token available from any source!');
    cachedToken = undefined;
  }

  console.log('🔑 getToken: Final token:', cachedToken ? cachedToken.substring(0, 50) + '...' : 'UNDEFINED');
  return cachedToken;
}

// Function to clear cached token and force refresh
export function refreshToken(): string | undefined {
  console.log('🔄 refreshToken: Clearing cached token and refreshing...');
  cachedToken = null;
  return getToken();
}

/* ------------------------------------------------------------------ */
/*                             CONFIG                                 */
/* ------------------------------------------------------------------ */

export const API_CONFIG = {
  BASE_URL: "https://dev.stt-user.acuizen.com",
  JWT_TOKEN:
    "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
} as const;

/* ------------------------------------------------------------------ */
/*                          API  SERVICE                              */
/* ------------------------------------------------------------------ */

type Json = Record<string, unknown> | unknown[];

export class ApiService {
  private static base = API_CONFIG.BASE_URL;

  /* ------------------------------ HELPERS ----------------------------- */

  private static authHeaders(additional: HeadersInit = {}): HeadersInit {
    const t = getToken();
    if (!t) throw new Error("No JWT available (user might be logged out)");

    if (isTokenExpired(t)) {
      console.warn("🚨 JWT appears expired – the request will likely fail");
    }

    return {
      Authorization: `Bearer ${t}`,
      ...additional,
    } as HeadersInit;
  }

  private static async request<T = Json>(
    method: "GET" | "POST" | "PATCH" | "DELETE",
    endpoint: string,
    body?: Json | FormData,
    extraHeaders: HeadersInit = {}
  ): Promise<T> {
    const url = `${this.base}${endpoint}`;
    console.log(`${method} → ${url}`);

    const isForm = body instanceof FormData;

    const headers = isForm
      ? this.authHeaders(extraHeaders) // don't set content‑type – browser will add boundary
      : this.authHeaders({
          "Content-Type": "application/json",
          Accept: "application/json",
          ...extraHeaders,
        });

    const res = await fetch(url, {
      method,
      headers,
      mode: "cors",
      body: body ? (isForm ? (body as FormData) : JSON.stringify(body)) : undefined,
    });

    if (!res.ok) {
      const txt = await res.text();
      throw new Error(`${method} ${endpoint} → ${res.status} :: ${txt}`);
    }

    // Some DELETE endpoints may return 204 (no body)
    if (res.status === 204) return undefined as unknown as T;

    return (await res.json()) as T;
  }

  /* ------------------------------- CRUD -------------------------------- */

  static get<T = Json>(e: string) {
    return this.request<T>("GET", e);
  }

  static post<T = Json>(e: string, d: Json) {
    return this.request<T>("POST", e, d);
  }

  static patch<T = Json>(e: string, d: Json) {
    return this.request<T>("PATCH", e, d);
  }

  static delete<T = Json>(e: string) {
    return this.request<T>("DELETE", e);
  }

  /* -------------------------- RESOURCE HELPERS ------------------------- */

  private static incidentFilter = {
    include: [
      "locationOne",
      "locationTwo",
      "locationThree",
      "locationFour",
      "locationFive",
      "locationSix",
      "incidentCircumstanceCategory",
      "incidentCircumstanceDescription",
      "incidentCircumstanceType",
      "lighting",
      "riskCategory",
      "surfaceCondition",
      "surfaceType",
      "workActivity",
      "reviewer",
      "user",
      "investigator",
    ],
  };

  /* Short‑hand wrappers from here down – keep only what you actually use */

  static getCurrentUser() {
    return this.get("/users/me");
  }

  static getLocationOnes() {
    return this.get("/location-ones");
  }

  static getLocationTwos(id: string) {
    return this.get(`/location-ones/${id}/location-twos`);
  }

  static uploadFiles(files: File[]) {
    const fd = new FormData();
    files.forEach((f) => fd.append("file", f));
    return this.request("POST", "/files", fd);
  }

  /* -------------------------- INCIDENTS EXAMPLE ------------------------ */

  static getAllIncidents() {
    const q = encodeURIComponent(JSON.stringify(this.incidentFilter));
    return this.get(`/all-report-incidents?filter=${q}`);
  }

  static createIncident(data: Json) {
    return this.post("/report-incidents", data);
  }

  // Additional methods needed by the test components
  static getReporterIncidents() {
    const q = encodeURIComponent(JSON.stringify(this.incidentFilter));
    return this.get(`/get-report-incidents-reporter?filter=${q}`);
  }

  static getReviewerActions() {
    return this.get("/actions/get/INCIDENT");
  }

  static getIncidentCircumstanceCategories() {
    return this.get("/incident-circumstance-categories");
  }

  static deleteIncident(id: string) {
    return this.delete(`/new-report-incidents/${id}`);
  }

  static getDynamicTitles() {
    return this.get("/dynamic-titles");
  }

  static getLocationThrees(locationTwoId: string) {
    return this.get(`/location-twos/${locationTwoId}/location-threes`);
  }

  static getLocationFours(locationThreeId: string) {
    return this.get(`/location-threes/${locationThreeId}/location-fours`);
  }

  static getLocationFives(locationFourId: string) {
    return this.get(`/location-fours/${locationFourId}/location-fives`);
  }

  static getLocationSixes(locationFiveId: string) {
    return this.get(`/location-fives/${locationFiveId}/location-sixes`);
  }

  static getIncidentCircumstanceTypes() {
    return this.get("/incident-circumstance-types");
  }

  static getUsers(params: any) {
    return this.post("/users/get_users", params);
  }

  static getAllNewIncidents() {
    const q = encodeURIComponent(JSON.stringify(this.incidentFilter));
    return this.get(`/all-new-report-incidents?filter=${q}`);
  }

  static createNewIncident(data: Json) {
    return this.post("/new-report-incidents", data);
  }

  static updateIncident(id: string, data: Json) {
    return this.patch(`/new-report-incidents/${id}`, data);
  }

  static submitIncidentReview(id: string, data: Json) {
    return this.patch(`/v2/report-incidents-review/${id}`, data);
  }

  static submitIncidentPreReview(id: string, data: Json) {
    return this.patch(`/v2/report-incidents-review-pre-submit/${id}`, data);
  }

  static updateIncidentReview(id: string, data: Json) {
    return this.patch(`/v2/report-incidents-review/${id}`, data);
  }

  static preSubmitIncidentReview(id: string, data: Json) {
    return this.patch(`/v2/report-incidents-review-pre-submit/${id}`, data);
  }

  // Environmental conditions API methods
  static getWeatherConditions() {
    return this.get("/weather-conditions");
  }

  static getSurfaceTypes() {
    return this.get("/surface-types");
  }

  static getSurfaceConditions() {
    return this.get("/surface-conditions");
  }

  static getLightings() {
    return this.get("/lightings");
  }

  static getAllUsersByLocation(locationData: any) {
    return this.post("/all-users-by-location", locationData);
  }

  static getLeadInvestigatorList(locationData: any) {
    return this.post("/lead-investigator-list", locationData);
  }

  static triggerInvestigation(id: string, data: { investigatorId: string; investigationRemarks: string }) {
    return this.patch(`/report-incidents-trigger-investigate/${id}`, data);
  }

  static saveIncidentInvestigation(id: string, data: { investigationStep: any }) {
    return this.patch(`/save-report-incidents-investigation/${id}`, data);
  }
}

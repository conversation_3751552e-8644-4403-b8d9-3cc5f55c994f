import React, { useState, useEffect } from "react";
import { Search, Filter, X, Eye, FileUp, ClipboardList, ArrowUpDown, AlertCircle, Trash2, Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Incident } from "@/contexts/IncidentContext";
import { format } from "date-fns";
import AttachmentUpload from "./AttachmentUpload";

interface AllIncidentsTableProps {
  data: Incident[];
  onView: (incident: Incident) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onAttachmentChange?: (id: string, attachments: File[]) => void;
  handleInvestigation?: (incidentId: string, status: string) => void;
  userRole?: 'reporter' | 'reviewer';
}

const AllIncidentsTable: React.FC<AllIncidentsTableProps> = ({
  data,
  onView,
  onEdit,
  onDelete,
  onAttachmentChange,
  handleInvestigation,
  userRole = 'reporter',
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<Incident[]>(data);
  const [typeFilters, setTypeFilters] = useState<string[]>([]);
  const [impactFilters, setImpactFilters] = useState<string[]>([]);
  const [statusFilters, setStatusFilters] = useState<string[]>([]);

  const [reportedByFilters, setReportedByFilters] = useState<string[]>([]);
  const [reviewedByFilters, setReviewedByFilters] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'ascending' | 'descending' } | null>(null);

  // Get unique values for filters
  const incidentTypes = Array.from(new Set(data.map(incident => incident.incidentType)));
  const impactClassifications = Array.from(new Set(data.map(incident => getImpactClassification(incident))));
  // Include all statuses including 'draft'
  const statuses = Array.from(new Set(data.map(incident => incident.status)));

  const reportedByUsers = Array.from(new Set(data.map(incident => incident.reportedBy)));
  const reviewedByUsers = Array.from(new Set(data.map(incident => incident.reviewedBy || "Not Reviewed")));

  // Helper function to get impact classification
  function getImpactClassification(incident: Incident): string {
    if (incident.injuryClassification?.isFatality) return "Fatality";
    if (incident.injuryClassification?.isPermanentDisability) return "Permanent Disability";
    if (incident.injuryClassification?.isLostTimeIncident) return "Lost Time Incident";
    if (incident.injuryClassification?.isMedicalTreatment) return "Medical Treatment";
    if (incident.injuryClassification?.isFirstAid) return "First Aid";
    return "Not Classified";
  }

  // Helper function to render impact classification badge
  const renderImpactClassification = (incident: Incident) => {
    const classification = getImpactClassification(incident);

    switch (classification) {
      case "Fatality":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Fatality</span>;
      case "Permanent Disability":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Permanent Disability</span>;
      case "Lost Time Incident":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Lost Time Incident</span>;
      case "Medical Treatment":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medical Treatment</span>;
      case "First Aid":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">First Aid</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Not Classified</span>;
    }
  };

  // Helper function to render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Draft</span>;
      case "submitted":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Reported</span>;
      case "under-review":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">Under Review</span>;
      case "investigation":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Under Investigation</span>;
      case "closed":
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Closed</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{status}</span>;
    }
  };

  // Helper function to determine workflow stage
  const determineWorkflowStage = (status: string): string => {
    switch (status) {
      case "draft":
        return "Draft";
      case "submitted":
        return "Preliminary Analysis in Progress";
      case "under-review":
        return "Supplementary information in Progress";
      case "investigation":
        return "Investigation";
      case "closed":
        return "Preliminary Analysis Completed";
      default:
        return "Unknown";
    }
  };

  // Update filtered data when search term or filters change
  useEffect(() => {
    // Use all incidents including drafts
    let result = [...data];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(incident =>
        (incident.maskId || incident.id).toLowerCase().includes(term) ||
        incident.description.toLowerCase().includes(term) ||
        (incident.classification || "").toLowerCase().includes(term)
      );
    }

    // Apply incident type filters
    if (typeFilters.length > 0) {
      result = result.filter(incident => typeFilters.includes(incident.incidentType));
    }

    // Apply impact classification filters
    if (impactFilters.length > 0) {
      result = result.filter(incident =>
        impactFilters.includes(getImpactClassification(incident))
      );
    }

    // Apply status filters
    if (statusFilters.length > 0) {
      result = result.filter(incident => statusFilters.includes(incident.status));
    }



    // Apply reported by filters
    if (reportedByFilters.length > 0) {
      result = result.filter(incident =>
        reportedByFilters.includes(incident.reportedBy)
      );
    }

    // Apply reviewed by filters
    if (reviewedByFilters.length > 0) {
      result = result.filter(incident =>
        reviewedByFilters.includes(incident.reviewedBy || "Not Reviewed")
      );
    }

    // Apply sorting
    if (sortConfig !== null) {
      result.sort((a, b) => {
        if (sortConfig.key === 'maskId') {
          const aMaskId = a.maskId || a.id;
          const bMaskId = b.maskId || b.id;
          return sortConfig.direction === 'ascending'
            ? aMaskId.localeCompare(bMaskId)
            : bMaskId.localeCompare(aMaskId);
        } else if (sortConfig.key === 'incidentDate') {
          return sortConfig.direction === 'ascending'
            ? a.incidentDate.getTime() - b.incidentDate.getTime()
            : b.incidentDate.getTime() - a.incidentDate.getTime();
        }
        return 0;
      });
    }

    setFilteredData(result);
  }, [
    data,
    searchTerm,
    typeFilters,
    impactFilters,
    statusFilters,

    reportedByFilters,
    reviewedByFilters,
    sortConfig
  ]);

  // Toggle filter functions
  const toggleTypeFilter = (type: string) => {
    setTypeFilters(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const toggleImpactFilter = (impact: string) => {
    setImpactFilters(prev =>
      prev.includes(impact)
        ? prev.filter(i => i !== impact)
        : [...prev, impact]
    );
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };



  const toggleReportedByFilter = (reporter: string) => {
    setReportedByFilters(prev =>
      prev.includes(reporter)
        ? prev.filter(r => r !== reporter)
        : [...prev, reporter]
    );
  };

  const toggleReviewedByFilter = (reviewer: string) => {
    setReviewedByFilters(prev =>
      prev.includes(reviewer)
        ? prev.filter(r => r !== reviewer)
        : [...prev, reviewer]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setTypeFilters([]);
    setImpactFilters([]);
    setStatusFilters([]);

    setReportedByFilters([]);
    setReviewedByFilters([]);
    setSortConfig(null);
  };

  // Handle sorting
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  // Get sort direction indicator
  const getSortDirectionIndicator = (key: string) => {
    if (!sortConfig || sortConfig.key !== key) {
      return null;
    }
    return sortConfig.direction === 'ascending' ? '↑' : '↓';
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start mb-6">
        <h2 className="text-xl font-semibold">All Incidents</h2>

        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search incidents..."
              className="w-full pl-9 h-10 bg-background border-muted"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-10 w-10 hover:bg-transparent"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {(searchTerm || typeFilters.length > 0 || impactFilters.length > 0 ||
           statusFilters.length > 0 ||
           reportedByFilters.length > 0 || reviewedByFilters.length > 0) && (
            <Button variant="ghost" onClick={clearFilters} className="h-10">
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      <div className="rounded-md border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow>
                <TableHead className="w-[100px]">
                  <div className="flex items-center cursor-pointer" onClick={() => requestSort('maskId')}>
                    Incident ID {getSortDirectionIndicator('maskId')}
                    <ArrowUpDown className="ml-1 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="w-[150px]">
                  <div className="flex items-center cursor-pointer" onClick={() => requestSort('incidentDate')}>
                    Incident Date {getSortDirectionIndicator('incidentDate')}
                    <ArrowUpDown className="ml-1 h-4 w-4" />
                  </div>
                </TableHead>
                <TableHead className="w-[200px]">Incident Title</TableHead>
                <TableHead className="w-[120px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-2 font-medium">
                        Category
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {incidentTypes.map((type) => (
                        <DropdownMenuCheckboxItem
                          key={type}
                          checked={typeFilters.includes(type)}
                          onCheckedChange={() => toggleTypeFilter(type)}
                        >
                          {type}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[150px]">Classification</TableHead>
                {/* <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-2 font-medium">
                        Impact Classification
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Impact</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {impactClassifications.map((impact) => (
                        <DropdownMenuCheckboxItem
                          key={impact}
                          checked={impactFilters.includes(impact)}
                          onCheckedChange={() => toggleImpactFilter(impact)}
                        >
                          {impact}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead> */}
                <TableHead className="w-[120px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-2 font-medium">
                        Incident Status
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {statuses.map((status) => (
                        <DropdownMenuCheckboxItem
                          key={status}
                          checked={statusFilters.includes(status)}
                          onCheckedChange={() => toggleStatusFilter(status)}
                        >
                          {status}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[120px]">Stage</TableHead>

                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-2 font-medium">
                        Reported By
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Reporter</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {reportedByUsers.map((reporter) => (
                        <DropdownMenuCheckboxItem
                          key={reporter}
                          checked={reportedByFilters.includes(reporter)}
                          onCheckedChange={() => toggleReportedByFilter(reporter)}
                        >
                          {reporter}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[150px]">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 flex items-center gap-1 -ml-2 font-medium">
                        Reviewed By
                        <Filter className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-56">
                      <DropdownMenuLabel>Filter by Reviewer</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {reviewedByUsers.map((reviewer) => (
                        <DropdownMenuCheckboxItem
                          key={reviewer}
                          checked={reviewedByFilters.includes(reviewer)}
                          onCheckedChange={() => toggleReviewedByFilter(reviewer)}
                        >
                          {reviewer}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableHead>
                <TableHead className="w-[120px] text-center">Actions Taken</TableHead>
                <TableHead className="w-[150px] text-center">Investigation Status</TableHead>
                <TableHead className="w-[120px] text-center">Attachment</TableHead>
                <TableHead className="w-[100px] text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((incident) => (
                  <TableRow key={incident.id || `${incident.incidentTitle}-${incident.reportedAt}`} className="hover:bg-muted/50">
                    <TableCell className="font-medium">
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-primary hover:text-primary/80"
                        onClick={() => onView(incident)}
                      >
                        {incident.maskId || incident.id}
                      </Button>
                    </TableCell>
                    <TableCell>{
                      incident.incidentDate && !isNaN(new Date(incident.incidentDate).getTime())
                        ? format(new Date(incident.incidentDate), "do MMM yyyy")
                        : "N/A"
                    }</TableCell>
                    <TableCell>
                      <div className="max-w-[180px] truncate font-medium" title={incident.incidentTitle || incident.description}>
                        {incident.incidentTitle || (incident.incidentType === "fall" ? "Fall Incident" : incident.description)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        at {incident.workplaceActivity || "Site"} {incident.incidentLocation || ""}
                      </div>
                    </TableCell>
                    <TableCell>{incident?.incidentCircumstanceCategory?.name}</TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {incident.classification || "Not Classified"}
                      </span>
                    </TableCell>
                    {/* <TableCell>
                      {renderImpactClassification(incident)}
                    </TableCell> */}
                    <TableCell>
                      {renderStatusBadge(incident.status)}
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {incident.stage ? incident.stage : determineWorkflowStage(incident.status)}
                      </span>
                    </TableCell>

                    <TableCell>{incident?.user?.firstName}</TableCell>
                    <TableCell>{incident?.reviewer?.firstName || "Not Reviewed"}</TableCell>
                    <TableCell className="text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {incident.actionsTakenCount || 0}/{incident.actionsTakenTotal || 0}
                      </span>
                    </TableCell>
                    <TableCell className="text-center">
                      {handleInvestigation && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-full"
                                onClick={() => handleInvestigation(incident.id, incident.investigationStatus)}
                              >
                                <div className="relative">
                                  <ClipboardList className="h-4 w-4" />
                                  {incident.investigationStatus === "open" && (
                                    <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-blue-500"></span>
                                  )}
                                </div>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Assign Lead Investigator</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                      {!handleInvestigation && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {incident.investigationStatus === "not-started" ? "Not Started" :
                           incident.investigationStatus === "in-progress" ? "In Progress" :
                           incident.investigationStatus === "completed" ? "Completed" :
                           incident.investigationStatus}
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <AttachmentUpload
                        incidentId={incident.id}
                        existingAttachments={incident.attachments?.map(url => {
                          // Create a File object from the URL
                          const dummyFile = new File([""], url, {
                            type: "image/jpeg",
                            lastModified: new Date().getTime()
                          });
                          // Store the URL in the file name for retrieval
                          Object.defineProperty(dummyFile, 'name', {
                            writable: true,
                            value: url
                          });
                          return dummyFile;
                        }) || []}
                        onAttachmentChange={
                          onAttachmentChange
                            ? (attachments) => onAttachmentChange(incident.id, attachments)
                            : undefined
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                onClick={() => onView(incident)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>View Details</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {onEdit && userRole === 'reporter' && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 rounded-full text-green-600 hover:text-green-800 hover:bg-green-50"
                                  onClick={() => onEdit(incident.id)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit Incident</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {onDelete && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50"
                                  onClick={() => onDelete(incident.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete Incident</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={15} className="text-center py-8 text-gray-500">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <AlertCircle className="h-8 w-8 mb-2 opacity-40" />
                      <p>No incidents found.</p>
                      <p className="text-sm">Try adjusting your filters or search criteria.</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default AllIncidentsTable;

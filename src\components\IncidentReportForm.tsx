import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { toast } from "sonner";
import { AlertTriangle, Calendar as CalendarIcon, Clock, Check, Info, Camera, HelpCircle } from "lucide-react";
import { useUser } from "@/contexts/UserContext";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { Calendar } from "@/components/ui/calendar";
import { TimePicker } from "@/components/ui/time-picker";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn, ApiService } from "@/lib/utils";

import FormStepIndicator from "./FormStepIndicator";
import PhotoUpload from "./PhotoUpload";
import IncidentPreview from "./IncidentPreview";
import { incidentFormSchema, IncidentFormValues } from "@/utils/validationSchema";

import {
  incidentTypes,
  incidentCategories,
  workplaceActivities,
  riskCategories,
  impactOptions,

  countries,
  cities,
  businessUnits,
  projectOptions,
  yesNoOptions,
  incidentReviewers,
} from "@/utils/formData";

// Ensure select is defined in this component's scope
const select = (selector: string) => document.querySelector(selector);

// Custom Toggle Button Component
interface ToggleButtonProps {
  value: boolean | null;
  onChange: (value: boolean) => void;
  disabled?: boolean;
  className?: string;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({ value, onChange, disabled = false, className = "" }) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        type="button"
        disabled={disabled}
        onClick={() => onChange(true)}
        className={`
          relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${value === true
            ? 'bg-green-500 border-green-500 text-white shadow-md'
            : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span className="text-xs font-medium">Yes</span>
      </button>
      <button
        type="button"
        disabled={disabled}
        onClick={() => onChange(false)}
        className={`
          relative inline-flex h-8 w-16 items-center justify-center rounded-full border-2 transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
          ${value === false
            ? 'bg-red-500 border-red-500 text-white shadow-md'
            : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span className="text-xs font-medium">No</span>
      </button>
    </div>
  );
};

const formSteps = [
  { title: "Basic Information" },
  { title: "Location" },
  { title: "Injury Classification" },
  { title: "Incident Reviewer" },
  { title: "Finalize" },
];

// Custom styles for form elements to match the reference image
const formLabelClass = "text-base font-medium mb-2";
const formInputClass = "h-12 border-2";
const formSectionTitleClass = "flex items-center gap-3 mb-6";
const formSectionIconClass = "flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary";

interface IncidentReportFormProps {
  onSuccess?: (incidentData?: any) => void;
  onIncidentCreated?: (incidentData?: any) => void;
  isDialog?: boolean;
  isEditMode?: boolean;
  existingIncident?: any;
  onNavigationChange?: (navigation: {
    canGoNext: boolean;
    canGoPrevious: boolean;
    currentStep: number;
    totalSteps: number;
    onNext: () => void;
    onPrevious: () => void;
    onSubmit: () => void;
    isLastStep: boolean;
  }) => void;
}

const IncidentReportForm: React.FC<IncidentReportFormProps> = ({
  onSuccess,
  onIncidentCreated,
  isDialog = false,
  isEditMode = false,
  existingIncident,
  onNavigationChange
}) => {
  const { userName } = useUser();
  const [step, setStep] = useState(1);
  const totalSteps = formSteps.length;
  const [incidentLevel, setIncidentLevel] = useState<string>("");
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  // API state for dynamic data
  const [locationOnes, setLocationOnes] = useState<{ id: string, name: string }[]>([]);
  const [locationTwos, setLocationTwos] = useState<{ id: string, name: string }[]>([]);
  const [locationThrees, setLocationThrees] = useState<{ id: string, name: string }[]>([]);
  const [locationFours, setLocationFours] = useState<{ id: string, name: string }[]>([]);
  const [incidentCategories, setIncidentCategories] = useState<{ id: string, name: string }[]>([]);
  const [incidentReviewers, setIncidentReviewers] = useState<{ id: string, firstName: string }[]>([]);
  const [dynamicTitles, setDynamicTitles] = useState<{ id: string, title: string, altTitle: string }[]>([]);

  const form = useForm<IncidentFormValues>({
    resolver: zodResolver(incidentFormSchema),
    defaultValues: {
      incidentTitle: "",
      incidentDate: new Date(),
      incidentTime: format(new Date(), "HH:mm"),
      incidentType: "",
      description: "",

      // Location information
      locationCountry: "",
      locationCity: "",
      locationBusinessUnit: "",
      locationProject: "",
      locationDetails: "",

      // Classification of Actual Injury
      isWorkRelated: null,
      lossOfConsciousness: null,
      isDangerousOccurrence: null,
      injuryClassification: {
        isFatality: null,
        isPermanentDisability: null,
        isLostTimeIncident: null,
        isMedicalTreatment: null,
        isFirstAid: null,
      },

      // Incident Reviewer
      incidentReviewer: "",

      // Optional fields from original form
      propertyDamage: false,
      propertyDamageDetails: "",
      incidentCategory: "",
      circumstances: "",
      workplaceActivity: "",
      riskCategories: [],
      photos: [],
      impactAssessment: {
        injury: "",
        environmentalDamage: "",
        productionLoss: "",
        reputationalDamage: "",
      },

      reportToAuthorities: false,
      authorityReportDetails: "",
      confirmAccuracy: false,
    },
    mode: "onChange",
  });

  // Pre-populate form when in edit mode
  useEffect(() => {
    if (isEditMode && existingIncident) {
      console.log("🔍 EditMode: Pre-populating form with incident data:", existingIncident);

      const appDetails = existingIncident.applicationDetails || {};
      console.log("🔍 EditMode: Application details:", appDetails);

      // Pre-populate form with existing incident data
      const formData = {
        incidentTitle: existingIncident.title || existingIncident.incidentTitle || existingIncident.description || "",
        incidentDate: existingIncident.incidentDate ? new Date(existingIncident.incidentDate) : new Date(),
        incidentTime: existingIncident.incidentTime || appDetails.incidentTime || format(new Date(), "HH:mm"),
        incidentType: existingIncident.incidentType || appDetails.incidentType || appDetails.IncidentCategory || "",
        description: existingIncident.description || appDetails.description || "",

        // Location data - try multiple sources
        locationCountry: appDetails.locationOneId || existingIncident.locationOneId || "",
        locationCity: appDetails.locationTwoId || existingIncident.locationTwoId || "",
        locationBusinessUnit: appDetails.locationThreeId || existingIncident.locationThreeId || "",
        locationProject: appDetails.locationFourId || existingIncident.locationFourId || "",
        locationDetails: appDetails.locationDetails || existingIncident.location || existingIncident.locationDetails || "",

        // Classification data
        isWorkRelated: appDetails.isWorkRelated ?? (appDetails.workRelatedDetails ? true : null),
        lossOfConsciousness: appDetails.lossOfConsciousness ?? (appDetails.lossOfConscious === "true" ? true : appDetails.lossOfConscious === "false" ? false : null),
        isDangerousOccurrence: appDetails.isDangerousOccurrence ?? (appDetails.dangerousOccurance === "true" ? true : appDetails.dangerousOccurance === "false" ? false : null),

        injuryClassification: {
          isFatality: appDetails.isFatality ?? (appDetails.fatality === "true" ? true : appDetails.fatality === "false" ? false : null),
          isPermanentDisability: appDetails.isPermanentDisability ?? null,
          isLostTimeIncident: appDetails.isLostTimeIncident ?? (appDetails.lostTime === "true" ? true : appDetails.lostTime === "false" ? false : null),
          isMedicalTreatment: appDetails.isMedicalTreatment ?? (appDetails.medicalTreatment === "true" ? true : appDetails.medicalTreatment === "false" ? false : null),
          isFirstAid: appDetails.isFirstAid ?? (appDetails.firstAid === "true" ? true : appDetails.firstAid === "false" ? false : null),
        },

        incidentReviewer: appDetails.incidentReviewer || appDetails.reviewerId || "",
        propertyDamage: appDetails.propertyDamage === "true" || appDetails.propertyDamage === true || false,
        propertyDamageDetails: appDetails.propertyDamageDetails || "",
        incidentCategory: existingIncident.incidentCategory || appDetails.incidentCategory || "",
        circumstances: appDetails.circumstances || "",
        workplaceActivity: appDetails.workplaceActivity || "",
        riskCategories: appDetails.riskCategories || [],
        photos: appDetails.uploads || appDetails.evidence || existingIncident.photos || [],

        impactAssessment: appDetails.impactAssessment || {
          injury: appDetails.actualImpact || "",
          environmentalDamage: "",
          productionLoss: "",
          reputationalDamage: "",
        },

        reportToAuthorities: appDetails.reportToAuthorities === "true" || appDetails.reportToAuthorities === true || appDetails.reportAuthority === "true" || false,
        authorityReportDetails: appDetails.authorityReportDetails || appDetails.authorityName || "",
        confirmAccuracy: false, // Always require re-confirmation
      };

      console.log("🔍 EditMode: Form data to populate:", formData);

      form.reset(formData);

      // Also trigger loading of dependent location data if we have location IDs
      if (formData.locationCountry) {
        setTimeout(() => loadLocationTwos(formData.locationCountry), 100);
      }
      if (formData.locationCity) {
        setTimeout(() => loadLocationThrees(formData.locationCity), 200);
      }
      if (formData.locationBusinessUnit) {
        setTimeout(() => loadLocationFours(formData.locationBusinessUnit), 300);
      }
      if (formData.locationProject) {
        setTimeout(() => loadIncidentReviewers(), 400);
      }
    }
  }, [isEditMode, existingIncident, form]);

  // Load API data on component mount
  useEffect(() => {
    loadLocationOnes();
    loadIncidentCategories();
    loadDynamicTitles();
  }, []);

  // API loading functions
  const loadIncidentCategories = async () => {
    try {
      console.log("🔄 Loading incident categories...");
      const response = await ApiService.getIncidentCircumstanceCategories();
      console.log("✅ Incident categories loaded:", response);
      setIncidentCategories(response || []);
    } catch (error) {
      console.error("❌ Failed to load incident categories:", error);
      toast.error("Failed to load incident categories");
    }
  };

  const loadLocationOnes = async () => {
    try {
      console.log("🔄 Loading location ones...");
      const response = await ApiService.getLocationOnes();
      console.log("✅ Location ones loaded:", response);
      setLocationOnes(response || []);
    } catch (error) {
      console.error("❌ Failed to load location ones:", error);
      toast.error("Failed to load locations");
    }
  };

  const loadLocationTwos = async (locationOneId: string) => {
    try {
      const response = await ApiService.getLocationTwos(locationOneId);
      setLocationTwos(response || []);
      // Reset dependent locations
      setLocationThrees([]);
      setLocationFours([]);
    } catch (error) {
      console.error("Failed to load location twos:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadLocationThrees = async (locationTwoId: string) => {
    try {
      const response = await ApiService.getLocationThrees(locationTwoId);
      setLocationThrees(response || []);
      // Reset dependent locations
      setLocationFours([]);
    } catch (error) {
      console.error("Failed to load location threes:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadLocationFours = async (locationThreeId: string) => {
    try {
      const response = await ApiService.getLocationFours(locationThreeId);
      setLocationFours(response || []);
    } catch (error) {
      console.error("Failed to load location fours:", error);
      toast.error("Failed to load sub-locations");
    }
  };

  const loadIncidentReviewers = async () => {
    try {
      console.log("🔄 Loading incident reviewers...");
      const locationOneId = form.watch("locationCountry");
      const locationTwoId = form.watch("locationCity");
      const locationThreeId = form.watch("locationBusinessUnit");
      const locationFourId = form.watch("locationProject");

      const params = {
        mode: "incident-reviewer",
        ...(locationOneId && { locationOneId }),
        ...(locationTwoId && { locationTwoId }),
        ...(locationThreeId && { locationThreeId }),
        ...(locationFourId && { locationFourId }),
      };

      const response = await ApiService.getUsers(params);
      console.log("✅ Incident reviewers loaded:", response);
      setIncidentReviewers(response || []);
    } catch (error) {
      console.error("❌ Failed to load incident reviewers:", error);
      toast.error("Failed to load incident reviewers");
    }
  };

  const loadDynamicTitles = async () => {
    try {
      console.log("🔄 Loading dynamic titles...");
      const response = await ApiService.getDynamicTitles();
      console.log("✅ Dynamic titles loaded:", response);
      setDynamicTitles(response || []);
    } catch (error) {
      console.error("❌ Failed to load dynamic titles:", error);
      toast.error("Failed to load dynamic titles");
    }
  };

  // Helper function to get dynamic title for location levels
  const getDynamicTitle = (locationLevel: string): string => {
    const titleMapping: Record<string, string> = {
      'LocationOne': dynamicTitles.find(t => t.title === 'LocationOne')?.altTitle || 'Country',
      'LocationTwo': dynamicTitles.find(t => t.title === 'LocationTwo')?.altTitle || 'City',
      'LocationThree': dynamicTitles.find(t => t.title === 'LocationThree')?.altTitle || 'Business Unit',
      'LocationFour': dynamicTitles.find(t => t.title === 'LocationFour')?.altTitle || 'Project/DC Name',
      'LocationFive': dynamicTitles.find(t => t.title === 'LocationFive')?.altTitle || 'Level',
      'LocationSix': dynamicTitles.find(t => t.title === 'LocationSix')?.altTitle || 'Zone',
    };
    return titleMapping[locationLevel] || locationLevel;
  };

  // Function to check and update completed steps
  const updateCompletedSteps = useCallback(async () => {
    const newCompletedSteps: number[] = [];

    // Check step 1: Basic Information
    const step1Fields = [
      "incidentTitle",
      "incidentDate",
      "incidentTime",
      "incidentType",
      "description"
    ] as const;
    const isStep1Complete = await form.trigger(step1Fields, { shouldFocus: false });
    if (isStep1Complete) newCompletedSteps.push(1);

    // Check step 2: Location
    const step2Fields = [
      "locationCountry",
      "locationCity",
      "locationBusinessUnit",
      "locationProject",
      "locationDetails"
    ] as const;
    const isStep2Complete = await form.trigger(step2Fields, { shouldFocus: false });
    if (isStep2Complete) newCompletedSteps.push(2);

    // Check step 3: Injury Classification
    const injuryClassification = form.getValues().injuryClassification;
    const isWorkRelated = form.getValues().isWorkRelated;
    const lossOfConsciousness = form.getValues().lossOfConsciousness;
    const isDangerousOccurrence = form.getValues().isDangerousOccurrence;

    // Check if any classification is selected or if all are explicitly set to false
    const isAnySelected =
      injuryClassification.isFatality === true ||
      injuryClassification.isPermanentDisability === true ||
      injuryClassification.isLostTimeIncident === true ||
      injuryClassification.isMedicalTreatment === true ||
      injuryClassification.isFirstAid === true;

    const isAllExplicitlyFalse =
      injuryClassification.isFatality === false &&
      injuryClassification.isPermanentDisability === false &&
      injuryClassification.isLostTimeIncident === false &&
      injuryClassification.isMedicalTreatment === false &&
      injuryClassification.isFirstAid === false;

    // Also check if the general classification questions are answered
    const areGeneralQuestionsAnswered =
      isWorkRelated !== null &&
      lossOfConsciousness !== null &&
      isDangerousOccurrence !== null;

    const isStep3Complete = (isAnySelected || isAllExplicitlyFalse) && areGeneralQuestionsAnswered;
    if (isStep3Complete) newCompletedSteps.push(3);

    // Check step 4: Incident Reviewer
    const isStep4Complete = await form.trigger(["incidentReviewer"] as const, { shouldFocus: false });
    if (isStep4Complete) newCompletedSteps.push(4);

    // Check step 5: Finalize
    const isStep5Complete = await form.trigger(["confirmAccuracy"] as const, { shouldFocus: false });
    if (isStep5Complete) newCompletedSteps.push(5);

    setCompletedSteps(newCompletedSteps);
  }, [form]);

  // Update completed steps whenever form values change
  useEffect(() => {
    // Watch all form values to trigger validation checks
    const subscription = form.watch(() => {
      // Run validation checks when form values change
      updateCompletedSteps();
    });

    // Initial check
    updateCompletedSteps();

    // Cleanup subscription
    return () => subscription.unsubscribe();
  }, [form, updateCompletedSteps]);

  const nextStep = useCallback(async () => {
    let fieldsToValidate: (keyof IncidentFormValues)[] = [];

    switch (step) {
      case 1: // Basic Information
        fieldsToValidate = [
          "incidentTitle",
          "incidentDate",
          "incidentTime",
          "incidentType",
          "description"
        ] as const;
        break;
      case 2: // Location
        fieldsToValidate = [
          "locationCountry",
          "locationCity",
          "locationBusinessUnit",
          "locationProject",
          "locationDetails"
        ] as const;
        break;
      case 3: // Injury Classification
        // No validation needed for boolean fields
        break;
      case 4: // Incident Reviewer
        fieldsToValidate = ["incidentReviewer"] as const;
        break;
      case 5: // Finalize
        fieldsToValidate = ["confirmAccuracy"] as const;
        break;
      default:
        break;
    }

    const result = await form.trigger(fieldsToValidate);
    if (result) {
      setStep((prev) => Math.min(prev + 1, totalSteps));
      window.scrollTo(0, 0);
    }
  }, [step, form, totalSteps]);

  const prevStep = useCallback(() => {
    setStep((prev) => Math.max(prev - 1, 1));
    window.scrollTo(0, 0);
  }, []);

  // Function to calculate and display the incident level based on the form values
  const calculateIncidentLevel = useCallback((formData?: IncidentFormValues) => {
    const data = formData || form.getValues();

    let level = "";

    // Check each classification level and determine the highest level selected
    if (data.injuryClassification.isFatality === true) {
      level = "Level 5 – Critical Incident";
    } else if (data.injuryClassification.isPermanentDisability === true) {
      level = "Level 4 – High Severity Incident";
    } else if (data.injuryClassification.isLostTimeIncident === true) {
      level = "Level 3 – LTI - Medium Severity Incident ";
    } else if (data.injuryClassification.isMedicalTreatment === true) {
      level = "Level 2 – Low Severity Incident (MTI)";
    } else if (data.injuryClassification.isFirstAid === true) {
      level = "Level 1 – Very Low Severity Incident (FAI)";
    } else if (
      data.injuryClassification.isFatality === false &&
      data.injuryClassification.isPermanentDisability === false &&
      data.injuryClassification.isLostTimeIncident === false &&
      data.injuryClassification.isMedicalTreatment === false &&
      data.injuryClassification.isFirstAid === false
    ) {
      // Only set to Near Miss if all questions are explicitly answered as No
      level = "Near Miss";
    }

    // Update the incident level state
    setIncidentLevel(level);
    return level;
  }, [form]);

  const onSubmit = useCallback(async (data: IncidentFormValues) => {
    console.log("Form submitted:", data);

    try {
      // Convert any null values to false for processing
      const processedData = {
        ...data,
        isWorkRelated: data.isWorkRelated === null ? false : data.isWorkRelated,
        lossOfConsciousness: data.lossOfConsciousness === null ? false : data.lossOfConsciousness,
        isDangerousOccurrence: data.isDangerousOccurrence === null ? false : data.isDangerousOccurrence,
        injuryClassification: {
          isFatality: data.injuryClassification.isFatality === null ? false : data.injuryClassification.isFatality,
          isPermanentDisability: data.injuryClassification.isPermanentDisability === null ? false : data.injuryClassification.isPermanentDisability,
          isLostTimeIncident: data.injuryClassification.isLostTimeIncident === null ? false : data.injuryClassification.isLostTimeIncident,
          isMedicalTreatment: data.injuryClassification.isMedicalTreatment === null ? false : data.injuryClassification.isMedicalTreatment,
          isFirstAid: data.injuryClassification.isFirstAid === null ? false : data.injuryClassification.isFirstAid,
        }
      };

      // Prepare the incident data for API submission (complete schema)
      const incidentData = {
        // Basic incident information
        title: processedData.incidentTitle,
        description: processedData.description,
        incidentDate: processedData.incidentDate instanceof Date ? processedData.incidentDate.toISOString().split('T')[0] : processedData.incidentDate,
        date: new Date().toISOString(),

        // Incident classification strings
        IncidentCategory: processedData.incidentType || "",
        dangerousOccurance: processedData.isDangerousOccurrence ? "true" : "false",
        fatality: processedData.injuryClassification.isFatality ? "true" : "false",
        injury: (processedData.injuryClassification.isFatality ||
          processedData.injuryClassification.isPermanentDisability ||
          processedData.injuryClassification.isLostTimeIncident ||
          processedData.injuryClassification.isMedicalTreatment ||
          processedData.injuryClassification.isFirstAid) ? "true" : "false",
        lostTime: processedData.injuryClassification.isLostTimeIncident ? "true" : "false",
        medicalTreatment: processedData.injuryClassification.isMedicalTreatment ? "true" : "false",
        firstAid: processedData.injuryClassification.isFirstAid ? "true" : "false",
        lossOfConscious: processedData.lossOfConsciousness ? "true" : "false",
        actualImpact: processedData.impactAssessment?.injury || "",
        potentialImpact: processedData.impactAssessment?.environmentalDamage || "",
        reportAuthority: processedData.reportToAuthorities ? "true" : "false",
        authorityName: processedData.authorityReportDetails || "",
        propertyDamage: processedData.propertyDamage ? "true" : "false",
        propertyDamageDetails: processedData.propertyDamageDetails || "",
        maskId: "",
        stopWorkOrder: "",
        workRelatedDetails: processedData.isWorkRelated ? "Work related incident" : "Non-work related incident",
        incidentData: JSON.stringify(processedData),
        created: new Date().toISOString(),
        classification: calculateIncidentLevel(processedData) || "",
        // incidentOwner: "",
        investigationRemarks: "",
        status: "under-review",
        informationStep: "",
        investigationStep: "",
        riskControl: "",

        // Boolean flags
        isWorkRelated: processedData.isWorkRelated || false,
        isInjury: (processedData.injuryClassification.isFatality ||
          processedData.injuryClassification.isPermanentDisability ||
          processedData.injuryClassification.isLostTimeIncident ||
          processedData.injuryClassification.isMedicalTreatment ||
          processedData.injuryClassification.isFirstAid) || false,
        isFirstAid: processedData.injuryClassification.isFirstAid || false,
        isPersonInjured: (processedData.injuryClassification.isFatality ||
          processedData.injuryClassification.isPermanentDisability ||
          processedData.injuryClassification.isLostTimeIncident ||
          processedData.injuryClassification.isMedicalTreatment ||
          processedData.injuryClassification.isFirstAid) || false,
        isMedicalTreatment: processedData.injuryClassification.isMedicalTreatment || false,
        isControlMeasure: false,
        isRiskAssessment: false,
        isMedicalLeave: false,
        investigationStatus: false,
        triggerInvestigationStatus: false,
        active: true,

        // Location hierarchy IDs
        locationOneId: processedData.locationCountry || "",
        locationTwoId: processedData.locationCity || "",
        locationThreeId: processedData.locationBusinessUnit || "",
        locationFourId: processedData.locationProject || "",
        locationFiveId: "",
        locationSixId: "",

        // File upload arrays
        uploads: [],
        evidence: processedData.photos || [],
        additionalDocuments: [],

        // Environmental and condition IDs - Map from form data where available
        lightingId: "", // No lighting field in current form
        surfaceTypeId: "", // No surface type field in current form
        surfaceConditionId: "", // No surface condition field in current form
        riskCategoryId: processedData.riskCategories?.length > 0 ? processedData.riskCategories[0] : "",
        incidentUnderlyingCauseId: "",
        incidentUnderlyingCauseTypeId: "",
        incidentUnderlyingCauseDescriptionId: "",
        incidentRootCauseTypeId: "",
        incidentRootCauseDescriptionId: "",
        weatherConditionId: "", // No weather field in current form
        workActivityId: processedData.workplaceActivity || "",
        incidentCircumstanceCategoryId: processedData.incidentType || "",
        incidentCircumstanceDescriptionId: processedData.circumstances || "",
        incidentCircumstanceTypeId: processedData.incidentCategory || "",
        version: "new",
        // User IDs
        userId: userName || "",
        reviewerId: processedData.incidentReviewer || "",
        investigatorId: "",
        // incidentOwnerId: "",
      };

      console.log("🔄 Submitting incident to API:", incidentData);

      let response;
      if (isEditMode && existingIncident) {
        // Update existing incident
        response = await ApiService.updateIncident(existingIncident.id, incidentData);

        toast.success("Incident updated successfully!", {
          duration: 5000,
          description: `Incident ${existingIncident.maskId || existingIncident.id} has been updated.`,
        });

        // Call onIncidentCreated for edit mode
        if (onIncidentCreated) {
          onIncidentCreated(response);
        }
      } else {
        // Create new incident
        response = await ApiService.createIncident(incidentData);

        toast.success("Report submitted successfully!", {
          duration: 5000,
          description: "Your incident report has been received and is being processed.",
        });

        form.reset();
        setStep(1);

        if (onSuccess) {
          onSuccess(response);
        }
      }

    } catch (error) {
      console.error("❌ Failed to submit incident:", error);
      toast.error("Failed to submit incident report", {
        duration: 5000,
        description: "Please try again or contact support if the problem persists.",
      });
    }
  }, [calculateIncidentLevel, userName, form, onSuccess]);

  // Watch all form values for the preview
  const formValues = form.watch();

  // Function to get the appropriate color class based on incident level
  const getClassificationColor = (level: string) => {
    if (level.includes("Level 5")) {
      return "bg-red-50 border-red-200 text-red-800";
    } else if (level.includes("Level 4")) {
      return "bg-orange-50 border-orange-200 text-orange-800";
    } else if (level.includes("Level 3")) {
      return "bg-amber-50 border-amber-200 text-amber-800";
    } else if (level.includes("Level 2")) {
      return "bg-yellow-50 border-yellow-200 text-yellow-800";
    } else if (level.includes("Level 1")) {
      return "bg-green-50 border-green-200 text-green-800";
    } else {
      return "bg-blue-50 border-blue-200 text-blue-700"; // Near Miss
    }
  };



  // Recalculate incident level when injury classification changes
  useEffect(() => {
    if (step === 3) {
      calculateIncidentLevel();
    }
  }, [
    formValues.injuryClassification.isFatality,
    formValues.injuryClassification.isPermanentDisability,
    formValues.injuryClassification.isLostTimeIncident,
    formValues.injuryClassification.isMedicalTreatment,
    formValues.injuryClassification.isFirstAid,
    step,
    calculateIncidentLevel
  ]);

  // Load incident reviewers when reaching step 4 (Incident Reviewer)
  useEffect(() => {
    if (step === 4) {
      loadIncidentReviewers();
    }
  }, [step]);

  const handleStepClick = (newStep: number) => {
    // Only allow going to previous steps or the next step
    if (newStep < step || newStep === step + 1) {
      setStep(newStep);
      window.scrollTo(0, 0);
    }
  };

  // Handle form submission from external trigger
  const handleExternalSubmit = useCallback(() => {
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  // Update navigation state when step changes
  useEffect(() => {
    if (onNavigationChange) {
      onNavigationChange({
        canGoNext: step < totalSteps,
        canGoPrevious: step > 1,
        currentStep: step,
        totalSteps,
        onNext: nextStep,
        onPrevious: prevStep,
        onSubmit: handleExternalSubmit,
        isLastStep: step === totalSteps,
      });
    }
  }, [step, totalSteps, onNavigationChange, nextStep, prevStep, handleExternalSubmit]);

  return (
    <div className={isDialog ? "w-full" : "w-full max-w-7xl mx-auto px-4 md:px-8 py-6"}>
      {!isDialog && (
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-2">Report an Incident</h1>
            <p className="text-muted-foreground">
              Please complete this form to report any incidents or near misses.
            </p>
          </div>
          <Button variant="outline" className="hidden md:flex items-center gap-2">
            View All Incidents
          </Button>
        </div>
      )}

      <div className={isDialog ? "" : "bg-card border rounded-lg shadow-sm p-6 md:p-8"}>
        {!isDialog && (
          <>
            <h2 className="text-xl font-semibold mb-6">Incident Information Required in Initial Reporting</h2>
            <p className="text-muted-foreground mb-6">
              Please provide all required information to report the incident accurately.
            </p>
          </>
        )}

        <FormStepIndicator
          totalSteps={totalSteps}
          currentStep={step}
          steps={formSteps}
          onStepClick={handleStepClick}
          completedSteps={completedSteps}
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {step === 1 && (
              <div className="form-section">
                <div className={cn(
                  formSectionTitleClass,
                  completedSteps.includes(1) ? "text-green-600" : ""
                )}>
                  <div className={cn(
                    formSectionIconClass,
                    completedSteps.includes(1) ? "bg-green-100 text-green-600" : ""
                  )}>
                    {completedSteps.includes(1) ? <Check size={20} /> : <Info size={20} />}
                  </div>
                  <h2 className={cn(
                    "text-xl font-semibold",
                    completedSteps.includes(1) ? "text-green-600" : ""
                  )}>Incident Information Required in Initial Reporting</h2>
                </div>

                <div className="space-y-6">
                  {/* Incident Title Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-purple-100 text-purple-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-heading-1"><path d="M4 12h8" /><path d="M4 18V6" /><path d="M12 18V6" /><path d="m17 12 3-2v8" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Incident Title</h3>
                    </div>

                    {/* Incident Title */}
                    <FormField
                      control={form.control}
                      name="incidentTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>1. Incident Title</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter a descriptive title for the incident"
                              {...field}
                              className={`${formInputClass} bg-white`}
                            />
                          </FormControl>
                          <FormDescription>
                            Free writing field - provide a clear, concise title
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Date and Time Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar-clock"><path d="M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7.5" /><path d="M16 2v4" /><path d="M8 2v4" /><path d="M3 10h18" /><circle cx="17" cy="16" r="3" /><path d="M17 14.5v1.5h1.5" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Date and Time</h3>
                    </div>

                    <div className="grid md:grid-cols-2 gap-x-6 gap-y-4">
                      {/* Incident Date */}
                      <FormField
                        control={form.control}
                        name="incidentDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className={formLabelClass}>2. Incident Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant={"outline"}
                                    className={cn(
                                      `w-full pl-3 text-left font-normal ${formInputClass} bg-white`,
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP")
                                    ) : (
                                      <span>Select date</span>
                                    )}
                                    <CalendarIcon className="ml-auto h-5 w-5 opacity-70" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                  className="pointer-events-auto"
                                />
                              </PopoverContent>
                            </Popover>
                            <FormDescription>
                              Calendar - select the date when the incident occurred
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Incident Time */}
                      <FormField
                        control={form.control}
                        name="incidentTime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>Incident Time</FormLabel>
                            <div className="relative">
                              <FormControl>
                                <TimePicker
                                  value={field.value}
                                  onChange={field.onChange}
                                  className={`${formInputClass} bg-white`}
                                />
                              </FormControl>
                            </div>
                            <FormDescription>
                              Enter the approximate time when the incident occurred
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Incident Type Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-list-filter"><path d="M3 6h18" /><path d="M7 12h10" /><path d="M10 18h4" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Incident Type</h3>
                    </div>

                    {/* Incident Type */}
                    <FormField
                      control={form.control}
                      name="incidentType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>3. Incident Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select incident type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {incidentCategories
                                .filter((category) => category.name !== "Environmental")
                                .map((category) => (
                                  <SelectItem key={category.id} value={category.id}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Select one - Health or Safety
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Description Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" /><polyline points="14 2 14 8 20 8" /><line x1="16" x2="8" y1="13" y2="13" /><line x1="16" x2="8" y1="17" y2="17" /><line x1="10" x2="8" y1="9" y2="9" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Incident Description</h3>
                    </div>

                    {/* Brief Description */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>4. Brief Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Provide a brief description of the incident..."
                              className="min-h-24 bg-white"
                              {...field}
                            />
                          </FormControl>
                          <div className="flex justify-between">
                            <FormDescription>
                              Free writing field - be specific and factual about what happened
                            </FormDescription>
                            <p className="text-xs text-gray-500">
                              {field.value.length}/1000 characters
                            </p>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="form-section">
                <div className={cn(
                  formSectionTitleClass,
                  completedSteps.includes(2) ? "text-green-600" : ""
                )}>
                  <div className={cn(
                    formSectionIconClass,
                    completedSteps.includes(2) ? "bg-green-100 text-green-600" : ""
                  )}>
                    {completedSteps.includes(2) ? <Check size={20} /> : <Info size={20} />}
                  </div>
                  <h2 className={cn(
                    "text-xl font-semibold",
                    completedSteps.includes(2) ? "text-green-600" : ""
                  )}>Location Information</h2>
                </div>

                {/* Location Information with improved layout */}
                <div className="space-y-6">
                  {/* Geographic Location Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" /><circle cx="12" cy="10" r="3" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Geographic Location</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                      {/* Location One */}
                      <FormField
                        control={form.control}
                        name="locationCountry"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className={formLabelClass}>5. {getDynamicTitle('LocationOne')}</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                // Reset dependent locations
                                form.setValue("locationCity", "");
                                form.setValue("locationBusinessUnit", "");
                                form.setValue("locationProject", "");
                                // Load next level
                                if (value) {
                                  loadLocationTwos(value);
                                }
                                // Load incident reviewers when location changes
                                setTimeout(() => loadIncidentReviewers(), 100);
                              }}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="bg-white">
                                  <SelectValue placeholder="Select location" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {locationOnes.map((location) => (
                                  <SelectItem key={location.id} value={location.id}>
                                    {location.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Location Two */}
                      <FormField
                        control={form.control}
                        name="locationCity"
                        render={({ field }) => {
                          const selectedLocationOne = form.watch("locationCountry");

                          return (
                            <FormItem>
                              <FormLabel className={formLabelClass}>{getDynamicTitle('LocationTwo')}</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  // Reset dependent locations
                                  form.setValue("locationBusinessUnit", "");
                                  form.setValue("locationProject", "");
                                  // Load next level
                                  if (value) {
                                    loadLocationThrees(value);
                                  }
                                  // Load incident reviewers when location changes
                                  setTimeout(() => loadIncidentReviewers(), 100);
                                }}
                                defaultValue={field.value}
                                disabled={!selectedLocationOne}
                              >
                                <FormControl>
                                  <SelectTrigger className="bg-white">
                                    <SelectValue placeholder="Select location" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {locationTwos.map((location) => (
                                    <SelectItem key={location.id} value={location.id}>
                                      {location.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  </div>

                  {/* Business Information Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-building-2"><path d="M6 22V2a1 1 0 0 1 1-1h9a1 1 0 0 1 1 1v20" /><path d="M12 13V7" /><path d="M10 7h4" /><path d="M10 13h4" /><path d="M6 22h12a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1H6" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Business Information</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                      {/* Location Three */}
                      <FormField
                        control={form.control}
                        name="locationBusinessUnit"
                        render={({ field }) => {
                          const selectedLocationTwo = form.watch("locationCity");

                          return (
                            <FormItem>
                              <FormLabel className={formLabelClass}>{getDynamicTitle('LocationThree')}</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  // Reset dependent location
                                  form.setValue("locationProject", "");
                                  // Load next level
                                  if (value) {
                                    loadLocationFours(value);
                                  }
                                  // Load incident reviewers when location changes
                                  setTimeout(() => loadIncidentReviewers(), 100);
                                }}
                                defaultValue={field.value}
                                disabled={!selectedLocationTwo}
                              >
                                <FormControl>
                                  <SelectTrigger className="bg-white">
                                    <SelectValue placeholder="Select location" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {locationThrees.map((location) => (
                                    <SelectItem key={location.id} value={location.id}>
                                      {location.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />

                      {/* Location Four */}
                      <FormField
                        control={form.control}
                        name="locationProject"
                        render={({ field }) => {
                          const selectedLocationThree = form.watch("locationBusinessUnit");

                          return (
                            <FormItem>
                              <FormLabel className={formLabelClass}>{getDynamicTitle('LocationFour')}</FormLabel>
                              <Select
                                onValueChange={(value) => {
                                  field.onChange(value);
                                  // Load incident reviewers when location changes
                                  setTimeout(() => loadIncidentReviewers(), 100);
                                }}
                                defaultValue={field.value}
                                disabled={!selectedLocationThree}
                              >
                                <FormControl>
                                  <SelectTrigger className="bg-white">
                                    <SelectValue placeholder="Select location" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {locationFours.map((location) => (
                                    <SelectItem key={location.id} value={location.id}>
                                      {location.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          );
                        }}
                      />
                    </div>
                  </div>

                  {/* Specific Location Details */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-amber-100 text-amber-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-locate-fixed"><line x1="2" x2="22" y1="12" y2="12" /><line x1="12" x2="12" y1="2" y2="22" /><circle cx="12" cy="12" r="7" /><circle cx="12" cy="12" r="3" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Specific Location</h3>
                    </div>

                    {/* Level and Location Details */}
                    <FormField
                      control={form.control}
                      name="locationDetails"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className={formLabelClass}>{getDynamicTitle('LocationFive')} and {getDynamicTitle('LocationSix')} Details</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter specific level and location details (e.g., Building 3, Floor 2, Room 201)"
                              {...field}
                              className={`${formInputClass} bg-white`}
                            />
                          </FormControl>
                          <FormDescription>
                            Provide specific details about the exact location where the incident occurred
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="form-section">
                <div className={cn(
                  formSectionTitleClass,
                  completedSteps.includes(3) ? "text-green-600" : ""
                )}>
                  <div className={cn(
                    formSectionIconClass,
                    completedSteps.includes(3) ? "bg-green-100 text-green-600" : ""
                  )}>
                    {completedSteps.includes(3) ? <Check size={20} /> : <AlertTriangle size={20} />}
                  </div>
                  <h2 className={cn(
                    "text-xl font-semibold",
                    completedSteps.includes(3) ? "text-green-600" : ""
                  )}>Classification of Actual Injury</h2>
                </div>
                <div className="space-y-6">
                  {/* General Classification Questions Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-blue-100 text-blue-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-clipboard-list"><rect width="8" height="4" x="8" y="2" rx="1" ry="1" /><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" /><path d="M12 11h4" /><path d="M12 16h4" /><path d="M8 11h.01" /><path d="M8 16h.01" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">General Classification Questions</h3>
                    </div>

                    <div className="space-y-4">
                      {/* Work Related Question */}
                      <FormField
                        control={form.control}
                        name="isWorkRelated"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">1. Is this incident work related?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Work-related incidents occur during work activities or as a result of performing work duties. Only work-related incidents are counted in official incident statistics.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Loss of Consciousness */}
                      <FormField
                        control={form.control}
                        name="lossOfConsciousness"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">2. Was there a loss of consciousness?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Loss of consciousness refers to any period where the injured person was unresponsive, regardless of duration. This is a serious medical condition that requires immediate attention.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Dangerous Occurrence */}
                      <FormField
                        control={form.control}
                        name="isDangerousOccurrence"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">3. Was this as a result of Dangerous Occurrence?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Dangerous Occurrence is an unplanned, uncontrolled event that had the potential to cause injury, ill health or damage, but did not actually do so. Examples include structural collapses, explosions, or equipment failures.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={field.onChange}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Injury Severity Classification Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-red-100 text-red-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-skull"><circle cx="9" cy="12" r="1" /><circle cx="15" cy="12" r="1" /><path d="M8 20v2h8v-2" /><path d="m12.5 17-.5-1-.5 1h1z" /><path d="M16 20a2 2 0 0 0 1.56-3.25 8 8 0 1 0-11.12 0A2 2 0 0 0 8 20" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Injury Severity Classification</h3>
                    </div>

                    <div className="space-y-4">
                      <div className="pb-1">
                        <h4 className="text-sm font-medium text-gray-600">Did this incident result in:</h4>
                      </div>

                      {/* Fatality */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFatality"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">A fatality?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A fatality is a death resulting from a work-related incident or occupational illness. This is classified as a Level 5 Critical Incident and requires immediate reporting to authorities.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={(value) => {
                                  field.onChange(value);
                                  calculateIncidentLevel();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Permanent Disability */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isPermanentDisability"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">An injury or occupational illness resulting in permanent disability?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Permanent disability refers to any injury or illness that results in permanent impairment of bodily functions, including loss of limbs, paralysis, or permanent damage to organs or senses. This is classified as a Level 4 High Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={(value) => {
                                  field.onChange(value);
                                  calculateIncidentLevel();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Lost Time Incident */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isLostTimeIncident"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Lost Time Incident (LTI)?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>A Lost Time Incident (LTI) is an injury or illness that results in the employee being unable to work for one or more scheduled workdays after the day of the incident. This is classified as a Level 3 Medium Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={(value) => {
                                  field.onChange(value);
                                  calculateIncidentLevel();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Medical Treatment Classification Group */}
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="p-1.5 rounded-full bg-green-100 text-green-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-first-aid-kit"><path d="M8 8V6a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" /><rect width="16" height="12" x="4" y="8" rx="2" /><path d="M12 12v4" /><path d="M10 14h4" /></svg>
                      </div>
                      <h3 className="text-sm font-medium text-gray-700">Medical Treatment Classification</h3>
                    </div>

                    <div className="space-y-4">
                      {/* Medical Treatment */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isMedicalTreatment"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Medical Treatment of Illness/Injury?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>Medical Treatment Injury (MTI) refers to injuries that require treatment beyond first aid, administered by a physician or other medical professional. This is classified as a Level 2 Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={(value) => {
                                  field.onChange(value);
                                  calculateIncidentLevel();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* First Aid */}
                      <FormField
                        control={form.control}
                        name="injuryClassification.isFirstAid"
                        render={({ field }) => (
                          <FormItem className="space-y-1">
                            <div className="flex items-center gap-1">
                              <FormLabel className="text-sm font-medium">Need to administer First Aid?</FormLabel>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span className="cursor-help text-muted-foreground">
                                      <HelpCircle size={14} />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <p>First Aid Injury (FAI) refers to minor injuries that can be treated on-site using basic first aid supplies and do not require professional medical care. This is classified as a Level 1 Very Low Severity Incident.</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <FormControl>
                              <ToggleButton
                                value={field.value}
                                onChange={(value) => {
                                  field.onChange(value);
                                  calculateIncidentLevel();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Incident Classification Result - Only show when there's a classification level */}
                  {incidentLevel && (
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 mt-6">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="p-1.5 rounded-full bg-purple-100 text-purple-600">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-badge-check"><path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" /><path d="m9 12 2 2 4-4" /></svg>
                        </div>
                        <h3 className="text-sm font-medium text-gray-700">Incident Classification Result</h3>
                      </div>

                      <div className={`p-4 rounded-md border ${getClassificationColor(incidentLevel)}`}>
                        <h3 className="font-bold text-base flex items-center">
                          <AlertTriangle className="mr-2 h-5 w-5" />
                          Final Classification
                        </h3>
                        <p className="mt-2 font-medium">
                          {incidentLevel}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {step === 4 && (
              <div className="form-section">
                <div className={cn(
                  formSectionTitleClass,
                  completedSteps.includes(4) ? "text-green-600" : ""
                )}>
                  <div className={cn(
                    formSectionIconClass,
                    completedSteps.includes(4) ? "bg-green-100 text-green-600" : ""
                  )}>
                    {completedSteps.includes(4) ? <Check size={20} /> : <Info size={20} />}
                  </div>
                  <h2 className={cn(
                    "text-xl font-semibold",
                    completedSteps.includes(4) ? "text-green-600" : ""
                  )}>Incident Reviewer</h2>
                </div>
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="incidentReviewer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className={formLabelClass}>7. Incident Reviewer</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select incident reviewer" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {incidentReviewers.map((reviewer) => (
                              <SelectItem key={reviewer.id} value={reviewer.id}>
                                {reviewer.firstName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {step === 5 && (
              <div className="form-section">
                <div className={cn(
                  formSectionTitleClass,
                  completedSteps.includes(5) ? "text-green-600" : ""
                )}>
                  <div className={cn(
                    formSectionIconClass,
                    completedSteps.includes(5) ? "bg-green-100 text-green-600" : ""
                  )}>
                    <Check size={20} />
                  </div>
                  <h2 className={cn(
                    "text-xl font-semibold",
                    completedSteps.includes(5) ? "text-green-600" : ""
                  )}>Finalize Report</h2>
                </div>
                <p className="mb-6 text-gray-600">
                  Please review your report for accuracy before submission. Once submitted, you will receive a
                  confirmation and the incident will be processed according to company procedures.
                </p>

                {/* Preview of all entered information */}
                <div className="mb-6">
                  <IncidentPreview
                    data={formValues}
                    locationOnes={locationOnes}
                    locationTwos={locationTwos}
                    locationThrees={locationThrees}
                    locationFours={locationFours}
                    incidentCategories={incidentCategories}
                    incidentReviewers={incidentReviewers}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="confirmAccuracy"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>I confirm that the information provided is accurate and complete</FormLabel>
                        <FormDescription>
                          By checking this box, you certify that you have provided truthful and accurate information to the best of your knowledge
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            )}



            {!isDialog && (
              <div className="flex justify-between pt-6 pb-2 border-t mt-8">
                {step > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={prevStep}
                  >
                    Previous
                  </Button>
                )}

                {step < totalSteps ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="ml-auto"
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="ml-auto"
                  >
                    Initiate Report
                  </Button>
                )}
              </div>
            )}
          </form>
        </Form>
      </div>
    </div>
  );
};

export default IncidentReportForm;
